#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
Observation 调试工具
用于详细打印和分析observation的结构和内容
"""

import json
import numpy as np
from typing import Dict, Any
import os


class ObservationDebugger:
    """Observation调试器"""
    
    def __init__(self, save_to_file=True, output_dir="debug_logs"):
        self.save_to_file = save_to_file
        self.output_dir = output_dir
        self.frame_count = 0
        
        if save_to_file and not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def debug_observation(self, observation: Dict[str, Any], agent_id=0, detailed=True):
        """
        详细调试observation
        
        Args:
            observation: 观测字典
            agent_id: 智能体ID
            detailed: 是否打印详细信息
        """
        self.frame_count += 1
        frame_no = observation.get("frame_state", {}).get("frameNo", self.frame_count)
        
        debug_info = self._analyze_observation(observation, frame_no, agent_id, detailed)
        
        # 打印到控制台
        print(debug_info)
        
        # 保存到文件
        if self.save_to_file:
            filename = f"{self.output_dir}/observation_frame_{frame_no}_agent_{agent_id}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(debug_info)
                
            # 同时保存JSON格式（便于程序分析）
            json_filename = f"{self.output_dir}/observation_frame_{frame_no}_agent_{agent_id}.json"
            self._save_observation_json(observation, json_filename)
    
    def _analyze_observation(self, observation: Dict[str, Any], frame_no: int, agent_id: int, detailed: bool) -> str:
        """分析observation并生成调试信息"""
        
        lines = []
        lines.append("=" * 80)
        lines.append(f"🎮 OBSERVATION ANALYSIS - Frame {frame_no}, Agent {agent_id}")
        lines.append("=" * 80)
        
        # 1. 基本信息
        lines.append(f"\n📋 Basic Info:")
        lines.append(f"   Keys: {list(observation.keys())}")
        lines.append(f"   Total Keys Count: {len(observation.keys())}")
        
        # 2. Frame State 详细分析
        if "frame_state" in observation:
            lines.extend(self._analyze_frame_state(observation["frame_state"], detailed))
        
        # 3. Legal Action 分析
        if "legal_action" in observation:
            lines.extend(self._analyze_legal_action(observation["legal_action"]))
        
        # 4. Reward 分析
        if "reward" in observation:
            lines.extend(self._analyze_reward(observation["reward"]))
        
        # 5. Sub Action Mask 分析
        if "sub_action_mask" in observation:
            lines.extend(self._analyze_sub_action_mask(observation["sub_action_mask"]))
        
        # 6. 其他字段分析
        other_keys = set(observation.keys()) - {"frame_state", "legal_action", "reward", "sub_action_mask"}
        if other_keys:
            lines.append(f"\n🔍 Other Fields:")
            for key in other_keys:
                value = observation[key]
                lines.append(f"   {key}: {type(value)} - {self._describe_value(value)}")
        
        lines.append("=" * 80)
        return "\n".join(lines)
    
    def _analyze_frame_state(self, frame_state: Dict[str, Any], detailed: bool) -> list:
        """分析frame_state"""
        lines = []
        lines.append(f"\n🎯 Frame State Analysis:")
        lines.append(f"   Frame No: {frame_state.get('frameNo', 'N/A')}")
        lines.append(f"   Keys: {list(frame_state.keys())}")
        
        # 英雄状态分析
        if "hero_states" in frame_state:
            heroes = frame_state["hero_states"]
            lines.append(f"\n   👥 Heroes ({len(heroes)} total):")
            for i, hero in enumerate(heroes):
                lines.extend(self._analyze_hero(hero, i, detailed))
        
        # NPC状态分析
        if "npc_states" in frame_state:
            npcs = frame_state["npc_states"]
            lines.append(f"\n   🤖 NPCs ({len(npcs)} total):")
            if detailed:
                for i, npc in enumerate(npcs[:3]):  # 只显示前3个NPC
                    lines.append(f"      NPC {i}: {self._describe_actor(npc)}")
                if len(npcs) > 3:
                    lines.append(f"      ... and {len(npcs) - 3} more NPCs")
            else:
                lines.append(f"      Total NPCs: {len(npcs)}")
        
        return lines
    
    def _analyze_hero(self, hero: Dict[str, Any], index: int, detailed: bool) -> list:
        """分析单个英雄"""
        lines = []
        actor_state = hero.get("actor_state", {})
        
        # 基本信息
        camp = actor_state.get("camp", "N/A")
        hp = actor_state.get("hp", 0)
        max_hp = actor_state.get("max_hp", 1)
        hp_percent = (hp / max_hp * 100) if max_hp > 0 else 0
        
        lines.append(f"      Hero {index}: Camp={camp}, HP={hp}/{max_hp} ({hp_percent:.1f}%)")
        
        if detailed:
            # 位置信息
            pos = actor_state.get("pos", {})
            if pos:
                x, y, z = pos.get("x", 0), pos.get("y", 0), pos.get("z", 0)
                lines.append(f"         Position: ({x:.2f}, {y:.2f}, {z:.2f})")
            
            # 其他状态
            level = actor_state.get("level", "N/A")
            lines.append(f"         Level: {level}")
            
            # 技能信息
            if "skill_states" in hero:
                skills = hero["skill_states"]
                lines.append(f"         Skills: {len(skills)} skills")
        
        return lines
    
    def _analyze_legal_action(self, legal_action) -> list:
        """分析legal_action"""
        lines = []
        lines.append(f"\n⚖️ Legal Action Analysis:")
        
        if hasattr(legal_action, 'shape'):
            lines.append(f"   Shape: {legal_action.shape}")
            lines.append(f"   Data Type: {legal_action.dtype}")
            lines.append(f"   Valid Actions: {np.sum(legal_action)}")
            lines.append(f"   Total Actions: {legal_action.size}")
            
            # 显示前10个动作的状态
            if legal_action.size <= 20:
                lines.append(f"   Actions: {legal_action.tolist()}")
            else:
                lines.append(f"   First 10: {legal_action.flatten()[:10].tolist()}")
        else:
            lines.append(f"   Type: {type(legal_action)}")
            lines.append(f"   Value: {legal_action}")
        
        return lines
    
    def _analyze_reward(self, reward) -> list:
        """分析reward"""
        lines = []
        lines.append(f"\n🏆 Reward Analysis:")
        
        if isinstance(reward, dict):
            for key, value in reward.items():
                lines.append(f"   {key}: {value}")
        else:
            lines.append(f"   Reward: {reward} (type: {type(reward)})")
        
        return lines
    
    def _analyze_sub_action_mask(self, sub_action_mask) -> list:
        """分析sub_action_mask"""
        lines = []
        lines.append(f"\n🎭 Sub Action Mask Analysis:")
        
        if hasattr(sub_action_mask, 'shape'):
            lines.append(f"   Shape: {sub_action_mask.shape}")
            lines.append(f"   Data Type: {sub_action_mask.dtype}")
            
            # 如果是2D数组，显示每个主动作的子动作数量
            if len(sub_action_mask.shape) == 2:
                lines.append(f"   Main Actions: {sub_action_mask.shape[0]}")
                lines.append(f"   Sub Actions per Main: {sub_action_mask.shape[1]}")
        else:
            lines.append(f"   Type: {type(sub_action_mask)}")
        
        return lines
    
    def _describe_actor(self, actor: Dict[str, Any]) -> str:
        """描述actor的基本信息"""
        actor_state = actor.get("actor_state", {})
        camp = actor_state.get("camp", "N/A")
        hp = actor_state.get("hp", 0)
        sub_type = actor.get("sub_type", "N/A")
        return f"Camp={camp}, HP={hp}, Type={sub_type}"
    
    def _describe_value(self, value) -> str:
        """描述值的基本信息"""
        if hasattr(value, 'shape'):
            return f"Shape={value.shape}, DType={getattr(value, 'dtype', 'N/A')}"
        elif isinstance(value, (list, tuple)):
            return f"Length={len(value)}"
        elif isinstance(value, dict):
            return f"Keys={len(value.keys())}"
        else:
            return str(value)[:50] + ("..." if len(str(value)) > 50 else "")
    
    def _save_observation_json(self, observation: Dict[str, Any], filename: str):
        """保存observation为JSON格式（处理numpy数组）"""
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        try:
            converted_obs = convert_numpy(observation)
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(converted_obs, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save JSON file {filename}: {e}")


# 使用示例
def example_usage():
    """使用示例"""
    debugger = ObservationDebugger(save_to_file=True, output_dir="observation_debug")
    
    # 模拟observation数据
    mock_observation = {
        "frame_state": {
            "frameNo": 100,
            "hero_states": [
                {
                    "actor_state": {
                        "camp": 1,
                        "hp": 800,
                        "max_hp": 1000,
                        "level": 5,
                        "pos": {"x": 10.5, "y": 20.3, "z": 0.0}
                    },
                    "skill_states": [{"id": 1}, {"id": 2}]
                }
            ]
        },
        "legal_action": np.array([1, 1, 0, 1, 0, 1]),
        "reward": {"reward_sum": 10.5, "kill_reward": 5.0},
        "sub_action_mask": np.random.rand(6, 16)
    }
    
    debugger.debug_observation(mock_observation, agent_id=0, detailed=True)


if __name__ == "__main__":
    example_usage()
