# 增强版防御塔特征配置文件
# 基于提供的特征表格设计

[feature_config]
# 基础特征 (9维)
in_main_hero_view = one_hot:1:eq
x_diff = min_max:-1:1
z_diff = min_max:-1:1
cake_need_scds = min_max:0:100
distance = min_max:0:1
location_x = min_max:-1:1
location_y = min_max:-1:1
location_z = min_max:-1:1

# 距离英雄特征 (10维)
distance_from_hero_0 = min_max:0:1
distance_from_hero_1 = min_max:0:1
distance_from_hero_2 = min_max:0:1
distance_from_hero_3 = min_max:0:1
distance_from_hero_4 = min_max:0:1
distance_from_hero_5 = min_max:0:1
distance_from_hero_6 = min_max:0:1
distance_from_hero_7 = min_max:0:1
distance_from_hero_8 = min_max:0:1
distance_from_hero_9 = min_max:0:1

# 攻击目标特征 (10维)
attack_target_0 = one_hot:1:eq
attack_target_1 = one_hot:1:eq
attack_target_2 = one_hot:1:eq
attack_target_3 = one_hot:1:eq
attack_target_4 = one_hot:1:eq
attack_target_5 = one_hot:1:eq
attack_target_6 = one_hot:1:eq
attack_target_7 = one_hot:1:eq
attack_target_8 = one_hot:1:eq
attack_target_9 = one_hot:1:eq

# 战斗相关特征 (8维)
nearby_emy_soldier_num = min_max:0:1
whether_under_frd_hero_atk = one_hot:1:eq
organ_emy_soldier_min_dist = min_max:0:1
is_emy_soldier_around_organ = one_hot:1:eq
organ_emy_soldier_cnt = min_max:0:1
is_organ_attack_emy_soldier = one_hot:1:eq
is_organ_attack_main_hero = one_hot:1:eq
conti_attack = one_hot:1:eq

# 属性特征 (10维)
is_alive = one_hot:1:eq
belong_to_main_camp = one_hot:1:eq
hp = min_max:0:1
hp_rate = min_max:0:1
max_hp = min_max:0:1
atk = min_max:0:1
kill_income = min_max:0:1
attack_range = min_max:0:1
attribute_reserved_1 = min_max:0:1
attribute_reserved_2 = min_max:0:1

# 类型特征 (3维) - one-hot编码
type_fixed_unit = one_hot:1:eq
type_mobile_unit = one_hot:1:eq
type_other = one_hot:1:eq

# 子类型特征 (5维) - one-hot编码
sub_type_tower_1 = one_hot:1:eq
sub_type_tower_2 = one_hot:1:eq
sub_type_tower_high = one_hot:1:eq
sub_type_tower_crystal = one_hot:1:eq
sub_type_other = one_hot:1:eq

# Buff特征 (24维)
# 自己的buff (6维: 1个标志位 + 5个buff详情)
self_buff_flag = one_hot:1:eq
self_buff_1 = min_max:0:1
self_buff_2 = min_max:0:1
self_buff_3 = min_max:0:1
self_buff_4 = min_max:0:1
self_buff_5 = min_max:0:1

# 队友的buff (6维: 1个标志位 + 5个buff详情)
teammate_buff_flag = one_hot:1:eq
teammate_buff_1 = min_max:0:1
teammate_buff_2 = min_max:0:1
teammate_buff_3 = min_max:0:1
teammate_buff_4 = min_max:0:1
teammate_buff_5 = min_max:0:1

# 敌方的buff (6维: 1个标志位 + 5个buff详情)
enemy_buff_flag = one_hot:1:eq
enemy_buff_1 = min_max:0:1
enemy_buff_2 = min_max:0:1
enemy_buff_3 = min_max:0:1
enemy_buff_4 = min_max:0:1
enemy_buff_5 = min_max:0:1

# NPC产生的buff (6维: 1个标志位 + 5个buff详情)
npc_buff_flag = one_hot:1:eq
npc_buff_1 = min_max:0:1
npc_buff_2 = min_max:0:1
npc_buff_3 = min_max:0:1
npc_buff_4 = min_max:0:1
npc_buff_5 = min_max:0:1

[feature_functions]
# 基础特征函数映射
in_main_hero_view = get_in_main_hero_view
x_diff = get_x_diff
z_diff = get_z_diff
cake_need_scds = get_cake_need_scds
distance = get_distance
location_x = get_location_x
location_y = get_location_y
location_z = get_location_z

# 距离英雄特征函数映射
distance_from_hero_0 = get_distance_from_hero_0
distance_from_hero_1 = get_distance_from_hero_1
distance_from_hero_2 = get_distance_from_hero_2
distance_from_hero_3 = get_distance_from_hero_3
distance_from_hero_4 = get_distance_from_hero_4
distance_from_hero_5 = get_distance_from_hero_5
distance_from_hero_6 = get_distance_from_hero_6
distance_from_hero_7 = get_distance_from_hero_7
distance_from_hero_8 = get_distance_from_hero_8
distance_from_hero_9 = get_distance_from_hero_9

# 攻击目标特征函数映射
attack_target_0 = get_attack_target_0
attack_target_1 = get_attack_target_1
attack_target_2 = get_attack_target_2
attack_target_3 = get_attack_target_3
attack_target_4 = get_attack_target_4
attack_target_5 = get_attack_target_5
attack_target_6 = get_attack_target_6
attack_target_7 = get_attack_target_7
attack_target_8 = get_attack_target_8
attack_target_9 = get_attack_target_9

# 战斗相关特征函数映射
nearby_emy_soldier_num = get_nearby_emy_soldier_num
whether_under_frd_hero_atk = get_whether_under_frd_hero_atk
organ_emy_soldier_min_dist = get_organ_emy_soldier_min_dist
is_emy_soldier_around_organ = get_is_emy_soldier_around_organ
organ_emy_soldier_cnt = get_organ_emy_soldier_cnt
is_organ_attack_emy_soldier = get_is_organ_attack_emy_soldier
is_organ_attack_main_hero = get_is_organ_attack_main_hero
conti_attack = get_conti_attack

# 属性特征函数映射
is_alive = get_is_alive
belong_to_main_camp = get_belong_to_main_camp
hp = get_hp
hp_rate = get_hp_rate
max_hp = get_max_hp
atk = get_atk
kill_income = get_kill_income
attack_range = get_attack_range
attribute_reserved_1 = get_attribute_reserved_1
attribute_reserved_2 = get_attribute_reserved_2

# 类型特征函数映射
type_fixed_unit = get_type_fixed_unit
type_mobile_unit = get_type_mobile_unit
type_other = get_type_other

# 子类型特征函数映射
sub_type_tower_1 = get_sub_type_tower_1
sub_type_tower_2 = get_sub_type_tower_2
sub_type_tower_high = get_sub_type_tower_high
sub_type_tower_crystal = get_sub_type_tower_crystal
sub_type_other = get_sub_type_other

# Buff特征函数映射
self_buff_flag = get_self_buff_flag
self_buff_1 = get_self_buff_1
self_buff_2 = get_self_buff_2
self_buff_3 = get_self_buff_3
self_buff_4 = get_self_buff_4
self_buff_5 = get_self_buff_5

teammate_buff_flag = get_teammate_buff_flag
teammate_buff_1 = get_teammate_buff_1
teammate_buff_2 = get_teammate_buff_2
teammate_buff_3 = get_teammate_buff_3
teammate_buff_4 = get_teammate_buff_4
teammate_buff_5 = get_teammate_buff_5

enemy_buff_flag = get_enemy_buff_flag
enemy_buff_1 = get_enemy_buff_1
enemy_buff_2 = get_enemy_buff_2
enemy_buff_3 = get_enemy_buff_3
enemy_buff_4 = get_enemy_buff_4
enemy_buff_5 = get_enemy_buff_5

npc_buff_flag = get_npc_buff_flag
npc_buff_1 = get_npc_buff_1
npc_buff_2 = get_npc_buff_2
npc_buff_3 = get_npc_buff_3
npc_buff_4 = get_npc_buff_4
npc_buff_5 = get_npc_buff_5

[feature_dimensions]
# 各类特征的维度配置
basic_features = 9
distance_heroes = 10
attack_target = 10
combat_features = 8
attribute_features = 10
type_features = 3
sub_type_features = 5
buff_features = 24
total_features = 79

[normalization_ranges]
# 归一化范围配置
position_range = 50000
distance_range = 20000
hp_range = 10000
attack_range = 2000
income_range = 500
view_distance = 15000
