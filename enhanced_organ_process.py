#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
增强版防御塔特征处理类
基于提供的特征表格实现完整的防御塔特征提取
"""

import math
import numpy as np
from collections import OrderedDict
from enum import Enum


class TowerType(Enum):
    """防御塔类型枚举"""
    TOWER_1 = 1      # 一塔
    TOWER_2 = 2      # 二塔
    TOWER_HIGH = 3   # 高地塔
    TOWER_CRYSTAL = 4 # 水晶塔


class UnitType(Enum):
    """单位类型枚举"""
    SOLDIER = 1      # 小兵
    MONSTER = 2      # 野怪
    SUMMON = 3       # 召唤物


class EnhancedOrganProcess:
    """增强版防御塔特征处理类"""
    
    def __init__(self, camp):
        self.main_camp = camp
        self.view_dist = 15000  # 视野距离
        self.attack_range_default = 1200  # 默认攻击范围
        
        # 缓存数据
        self.main_hero_info = None
        self.main_camp_heroes = {}
        self.enemy_camp_heroes = {}
        self.all_soldiers = []
        self.towers = []
        
        # 特征维度配置
        self.feature_dims = {
            'basic_features': 9,      # 基础特征维度
            'distance_heroes': 10,    # 距离英雄特征维度
            'attack_target': 10,      # 攻击目标特征维度
            'combat_features': 8,     # 战斗特征维度
            'attribute_features': 10, # 属性特征维度
            'type_features': 3,       # 类型特征维度
            'sub_type_features': 5,   # 子类型特征维度
            'buff_features': 24,      # Buff特征维度
        }
        
        self.total_feature_dim = sum(self.feature_dims.values())
    
    def process_tower_features(self, frame_state):
        """处理防御塔特征的主函数"""
        # 更新缓存数据
        self._update_cache_data(frame_state)
        
        tower_features = []
        
        # 处理敌方防御塔
        enemy_towers = [tower for tower in self.towers if tower['camp'] != self.main_camp]
        
        for tower in enemy_towers:
            features = self._extract_single_tower_features(tower)
            tower_features.extend(features)
        
        # 如果没有敌方防御塔，填充零特征
        if not enemy_towers:
            tower_features.extend([0.0] * self.total_feature_dim)
        
        return tower_features
    
    def _update_cache_data(self, frame_state):
        """更新缓存数据"""
        # 更新英雄信息
        self.main_camp_heroes.clear()
        self.enemy_camp_heroes.clear()
        
        for hero in frame_state.get("hero_states", []):
            camp = hero["actor_state"]["camp"]
            if camp == self.main_camp:
                self.main_camp_heroes[hero["actor_state"]["runtime_id"]] = hero
                if not self.main_hero_info:  # 假设第一个是主英雄
                    self.main_hero_info = hero
            else:
                self.enemy_camp_heroes[hero["actor_state"]["runtime_id"]] = hero
        
        # 更新NPC信息（包括防御塔和小兵）
        self.towers.clear()
        self.all_soldiers.clear()
        
        for npc in frame_state.get("npc_states", []):
            if npc.get("sub_type") == "ACTOR_SUB_TOWER":
                self.towers.append(npc)
            elif npc.get("sub_type") == "ACTOR_SUB_soldier":
                self.all_soldiers.append(npc)
    
    def _extract_single_tower_features(self, tower):
        """提取单个防御塔的所有特征"""
        features = []
        
        # 1. 基础特征 (9维)
        features.extend(self._get_basic_features(tower))
        
        # 2. 距离英雄特征 (10维)
        features.extend(self._get_distance_from_heroes(tower))
        
        # 3. 攻击目标特征 (10维)
        features.extend(self._get_attack_target_features(tower))
        
        # 4. 战斗相关特征 (8维)
        features.extend(self._get_combat_features(tower))
        
        # 5. 属性特征 (10维)
        features.extend(self._get_attribute_features(tower))
        
        # 6. 类型特征 (3维)
        features.extend(self._get_type_features(tower))
        
        # 7. 子类型特征 (5维)
        features.extend(self._get_sub_type_features(tower))
        
        # 8. Buff特征 (24维)
        features.extend(self._get_buff_features(tower))
        
        return features
    
    def _get_basic_features(self, tower):
        """获取基础特征 (9维)"""
        features = []
        
        if not self.main_hero_info:
            return [0.0] * 9
        
        main_hero_pos = self.main_hero_info["actor_state"]["pos"]
        tower_pos = tower["actor_state"]["pos"]
        
        # 1. 是否在主英雄视野中
        in_view = 1.0 if self._is_in_view(main_hero_pos, tower_pos) else 0.0
        features.append(in_view)
        
        # 2. x坐标差值 (归一化)
        x_diff = (tower_pos["x"] - main_hero_pos["x"]) / 30000.0
        features.append(max(-1.0, min(1.0, x_diff)))
        
        # 3. z坐标差值 (归一化)
        z_diff = (tower_pos["z"] - main_hero_pos["z"]) / 30000.0
        features.append(max(-1.0, min(1.0, z_diff)))
        
        # 4. cake剩余cd时间 (暂时设为0)
        features.append(0.0)
        
        # 5. 局部距离 (归一化)
        distance = self._calculate_distance(main_hero_pos, tower_pos)
        normalized_distance = min(1.0, distance / 20000.0)
        features.append(normalized_distance)
        
        # 6-8. 全局位置 x, y, z (归一化)
        features.append(tower_pos["x"] / 50000.0)
        features.append(tower_pos.get("y", 0) / 1000.0)  # y坐标通常较小
        features.append(tower_pos["z"] / 50000.0)
        
        return features
    
    def _get_distance_from_heroes(self, tower):
        """获取距离各英雄的距离特征 (10维)"""
        features = []
        tower_pos = tower["actor_state"]["pos"]
        
        # 收集所有英雄
        all_heroes = list(self.main_camp_heroes.values()) + list(self.enemy_camp_heroes.values())
        
        # 计算距离前10个英雄的距离
        for i in range(10):
            if i < len(all_heroes):
                hero_pos = all_heroes[i]["actor_state"]["pos"]
                distance = self._calculate_distance(tower_pos, hero_pos)
                normalized_distance = min(1.0, distance / 20000.0)
                features.append(normalized_distance)
            else:
                features.append(1.0)  # 如果英雄不存在，设为最大距离
        
        return features
    
    def _get_attack_target_features(self, tower):
        """获取攻击目标特征 (10维)"""
        # 这里需要根据实际游戏数据结构来实现
        # 暂时返回零特征
        return [0.0] * 10
    
    def _get_combat_features(self, tower):
        """获取战斗相关特征 (8维)"""
        features = []
        tower_pos = tower["actor_state"]["pos"]
        tower_camp = tower["actor_state"]["camp"]
        attack_range = tower["actor_state"].get("attack_range", self.attack_range_default)
        
        # 1. 主英雄所在的敌对塔下的主英雄阵营方小兵个数
        nearby_soldiers = self._count_nearby_friendly_soldiers(tower_pos, attack_range)
        features.append(min(1.0, nearby_soldiers / 10.0))  # 归一化
        
        # 2. 塔是否正在被友方英雄攻击
        under_attack = self._is_under_friendly_hero_attack(tower)
        features.append(1.0 if under_attack else 0.0)
        
        # 3. 与塔敌对的最近小兵距离
        min_enemy_soldier_dist = self._get_min_enemy_soldier_distance(tower_pos, tower_camp)
        normalized_dist = min(1.0, min_enemy_soldier_dist / 5000.0)
        features.append(normalized_dist)
        
        # 4. 塔攻击范围内是否有敌方小兵
        has_enemy_soldiers = self._has_enemy_soldiers_in_range(tower_pos, tower_camp, attack_range)
        features.append(1.0 if has_enemy_soldiers else 0.0)
        
        # 5. 塔攻击范围内敌方小兵个数
        enemy_soldier_count = self._count_enemy_soldiers_in_range(tower_pos, tower_camp, attack_range)
        features.append(min(1.0, enemy_soldier_count / 10.0))
        
        # 6. 是否正在攻击小兵
        attacking_soldier = self._is_attacking_soldier(tower)
        features.append(1.0 if attacking_soldier else 0.0)
        
        # 7. 是否正在攻击主英雄
        attacking_main_hero = self._is_attacking_main_hero(tower)
        features.append(1.0 if attacking_main_hero else 0.0)
        
        # 8. 是否持续攻击主英雄
        continuous_attack = self._is_continuous_attack_main_hero(tower)
        features.append(1.0 if continuous_attack else 0.0)
        
        return features
    
    def _get_attribute_features(self, tower):
        """获取属性特征 (10维)"""
        features = []
        actor_state = tower["actor_state"]
        
        # 1. 是否存活
        is_alive = 1.0 if actor_state.get("hp", 0) > 0 else 0.0
        features.append(is_alive)
        
        # 2. 是否属于我方阵营
        belong_to_main = 1.0 if actor_state["camp"] == self.main_camp else 0.0
        features.append(belong_to_main)
        
        # 3. 血量绝对值 (归一化)
        hp = actor_state.get("hp", 0)
        normalized_hp = min(1.0, hp / 10000.0)  # 假设最大血量10000
        features.append(normalized_hp)
        
        # 4. 血量百分比
        max_hp = actor_state.get("max_hp", 1)
        hp_rate = hp / max_hp if max_hp > 0 else 0.0
        features.append(hp_rate)
        
        # 5. 血量最大值 (归一化)
        normalized_max_hp = min(1.0, max_hp / 10000.0)
        features.append(normalized_max_hp)
        
        # 6. 攻击力 (归一化)
        atk = actor_state.get("atk", 0)
        normalized_atk = min(1.0, atk / 1000.0)
        features.append(normalized_atk)
        
        # 7. 击杀收入 (归一化)
        kill_income = actor_state.get("kill_income", 0)
        normalized_income = min(1.0, kill_income / 500.0)
        features.append(normalized_income)
        
        # 8. 攻击范围 (归一化)
        attack_range = actor_state.get("attack_range", self.attack_range_default)
        normalized_range = min(1.0, attack_range / 2000.0)
        features.append(normalized_range)
        
        # 9-10. 预留属性
        features.extend([0.0, 0.0])
        
        return features
    
    def _get_type_features(self, tower):
        """获取类型特征 (3维) - one-hot编码"""
        # 根据sub_type判断单位类型
        sub_type = tower.get("sub_type", "")
        
        # 假设防御塔都是固定单位类型
        return [1.0, 0.0, 0.0]  # [固定单位, 移动单位, 其他]
    
    def _get_sub_type_features(self, tower):
        """获取子类型特征 (5维) - one-hot编码"""
        # 根据防御塔的具体类型进行编码
        sub_type = tower.get("sub_type", "")
        config_id = tower["actor_state"].get("config_id", 0)
        
        # 这里需要根据实际的config_id来判断塔的类型
        # 暂时返回默认值
        return [1.0, 0.0, 0.0, 0.0, 0.0]  # [一塔, 二塔, 高地塔, 水晶塔, 其他]
    
    def _get_buff_features(self, tower):
        """获取Buff特征 (24维)"""
        # Buff特征比较复杂，需要根据实际的buff系统来实现
        # 暂时返回零特征
        return [0.0] * 24
    
    # 辅助函数
    def _is_in_view(self, hero_pos, tower_pos):
        """判断是否在视野内"""
        x_diff = abs(hero_pos["x"] - tower_pos["x"])
        z_diff = abs(hero_pos["z"] - tower_pos["z"])
        return x_diff <= self.view_dist and z_diff <= self.view_dist
    
    def _calculate_distance(self, pos1, pos2):
        """计算两点间距离"""
        x_diff = pos1["x"] - pos2["x"]
        z_diff = pos1["z"] - pos2["z"]
        return math.sqrt(x_diff * x_diff + z_diff * z_diff)
    
    def _count_nearby_friendly_soldiers(self, tower_pos, attack_range):
        """计算附近友方小兵数量"""
        count = 0
        for soldier in self.all_soldiers:
            if soldier["actor_state"]["camp"] == self.main_camp:
                soldier_pos = soldier["actor_state"]["pos"]
                distance = self._calculate_distance(tower_pos, soldier_pos)
                if distance <= attack_range:
                    count += 1
        return count
    
    def _is_under_friendly_hero_attack(self, tower):
        """判断是否被友方英雄攻击"""
        # 这需要根据实际的攻击状态数据来实现
        return False
    
    def _get_min_enemy_soldier_distance(self, tower_pos, tower_camp):
        """获取最近敌方小兵距离"""
        min_dist = float('inf')
        for soldier in self.all_soldiers:
            if soldier["actor_state"]["camp"] != tower_camp:
                soldier_pos = soldier["actor_state"]["pos"]
                distance = self._calculate_distance(tower_pos, soldier_pos)
                min_dist = min(min_dist, distance)
        return min_dist if min_dist != float('inf') else 10000.0
    
    def _has_enemy_soldiers_in_range(self, tower_pos, tower_camp, attack_range):
        """判断攻击范围内是否有敌方小兵"""
        for soldier in self.all_soldiers:
            if soldier["actor_state"]["camp"] != tower_camp:
                soldier_pos = soldier["actor_state"]["pos"]
                distance = self._calculate_distance(tower_pos, soldier_pos)
                if distance <= attack_range:
                    return True
        return False
    
    def _count_enemy_soldiers_in_range(self, tower_pos, tower_camp, attack_range):
        """计算攻击范围内敌方小兵数量"""
        count = 0
        for soldier in self.all_soldiers:
            if soldier["actor_state"]["camp"] != tower_camp:
                soldier_pos = soldier["actor_state"]["pos"]
                distance = self._calculate_distance(tower_pos, soldier_pos)
                if distance <= attack_range:
                    count += 1
        return count
    
    def _is_attacking_soldier(self, tower):
        """判断是否正在攻击小兵"""
        # 需要根据实际的攻击目标数据来实现
        return False
    
    def _is_attacking_main_hero(self, tower):
        """判断是否正在攻击主英雄"""
        # 需要根据实际的攻击目标数据来实现
        return False
    
    def _is_continuous_attack_main_hero(self, tower):
        """判断是否持续攻击主英雄"""
        # 需要根据实际的攻击历史数据来实现
        return False


# 使用示例
def example_usage():
    """使用示例"""
    # 创建处理器
    processor = EnhancedOrganProcess(camp=1)
    
    # 模拟frame_state数据
    frame_state = {
        "hero_states": [
            {
                "actor_state": {
                    "camp": 1,
                    "runtime_id": 1001,
                    "pos": {"x": 1000, "y": 0, "z": 2000},
                    "hp": 1000,
                    "max_hp": 1000
                }
            }
        ],
        "npc_states": [
            {
                "sub_type": "ACTOR_SUB_TOWER",
                "actor_state": {
                    "camp": 2,
                    "runtime_id": 2001,
                    "pos": {"x": 5000, "y": 0, "z": 6000},
                    "hp": 3000,
                    "max_hp": 5000,
                    "atk": 200,
                    "attack_range": 1200
                }
            }
        ]
    }
    
    # 提取特征
    features = processor.process_tower_features(frame_state)
    print(f"提取的防御塔特征维度: {len(features)}")
    print(f"特征值: {features[:10]}...")  # 只显示前10个特征值


if __name__ == "__main__":
    example_usage()
