#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
防御塔特征集成示例
展示如何将增强版防御塔特征处理集成到现有系统中
"""

import numpy as np
from enhanced_organ_process import EnhancedOrganProcess


class FeatureIntegrationExample:
    """特征集成示例类"""
    
    def __init__(self, camp):
        self.camp = camp
        # 创建增强版防御塔处理器
        self.enhanced_organ_processor = EnhancedOrganProcess(camp)
        
        # 原有的其他特征处理器（示例）
        self.hero_processor = None  # 英雄特征处理器
        self.soldier_processor = None  # 小兵特征处理器
        
    def process_all_features(self, observation):
        """处理所有特征的主函数"""
        frame_state = observation["frame_state"]
        
        # 1. 处理防御塔特征（新增的增强版）
        tower_features = self.enhanced_organ_processor.process_tower_features(frame_state)
        
        # 2. 处理其他特征（原有的）
        # hero_features = self.process_hero_features(frame_state)
        # soldier_features = self.process_soldier_features(frame_state)
        # other_features = self.process_other_features(frame_state)
        
        # 3. 合并所有特征
        all_features = []
        all_features.extend(tower_features)
        # all_features.extend(hero_features)
        # all_features.extend(soldier_features)
        # all_features.extend(other_features)
        
        return np.array(all_features, dtype=np.float32)
    
    def analyze_tower_features(self, observation):
        """分析防御塔特征的详细信息"""
        frame_state = observation["frame_state"]
        
        print("="*80)
        print("防御塔特征分析")
        print("="*80)
        
        # 获取防御塔特征
        tower_features = self.enhanced_organ_processor.process_tower_features(frame_state)
        
        print(f"总特征维度: {len(tower_features)}")
        print(f"预期维度: {self.enhanced_organ_processor.total_feature_dim}")
        
        # 按类别分析特征
        feature_dims = self.enhanced_organ_processor.feature_dims
        start_idx = 0
        
        for feature_type, dim in feature_dims.items():
            end_idx = start_idx + dim
            feature_slice = tower_features[start_idx:end_idx]
            
            print(f"\n{feature_type} ({dim}维):")
            print(f"  范围: [{start_idx}:{end_idx}]")
            print(f"  值: {feature_slice[:5]}..." if len(feature_slice) > 5 else f"  值: {feature_slice}")
            print(f"  统计: min={min(feature_slice):.3f}, max={max(feature_slice):.3f}, mean={np.mean(feature_slice):.3f}")
            
            start_idx = end_idx
        
        print("="*80)
        
        return tower_features
    
    def compare_with_original(self, observation):
        """与原有特征处理进行对比"""
        print("="*80)
        print("特征对比分析")
        print("="*80)
        
        # 新版特征
        new_features = self.enhanced_organ_processor.process_tower_features(observation["frame_state"])
        
        # 如果有原版特征处理器，可以在这里对比
        # original_features = self.original_organ_processor.process_vec_organ(observation["frame_state"])
        
        print(f"新版特征维度: {len(new_features)}")
        # print(f"原版特征维度: {len(original_features)}")
        
        print(f"新版特征前10个值: {new_features[:10]}")
        # print(f"原版特征前10个值: {original_features[:10]}")
        
        print("="*80)


def create_test_observation():
    """创建测试用的observation数据"""
    observation = {
        "env_id": 12345,
        "player_id": 0,
        "player_camp": 1,
        "win": 0,
        "frame_state": {
            "frameNo": 100,
            "hero_states": [
                {
                    "actor_state": {
                        "camp": 1,
                        "runtime_id": 1001,
                        "config_id": 169,
                        "pos": {"x": 1000, "y": 0, "z": 2000},
                        "hp": 1000,
                        "max_hp": 1000,
                        "atk": 100,
                        "level": 5
                    }
                },
                {
                    "actor_state": {
                        "camp": 2,
                        "runtime_id": 2001,
                        "config_id": 169,
                        "pos": {"x": 8000, "y": 0, "z": 9000},
                        "hp": 950,
                        "max_hp": 1000,
                        "atk": 105,
                        "level": 4
                    }
                }
            ],
            "npc_states": [
                # 敌方防御塔
                {
                    "sub_type": "ACTOR_SUB_TOWER",
                    "actor_state": {
                        "camp": 2,
                        "runtime_id": 3001,
                        "config_id": 501,  # 假设501是一塔的config_id
                        "pos": {"x": 10000, "y": 0, "z": 12000},
                        "hp": 4000,
                        "max_hp": 5000,
                        "atk": 300,
                        "attack_range": 1200,
                        "kill_income": 200
                    }
                },
                # 友方小兵
                {
                    "sub_type": "ACTOR_SUB_soldier",
                    "actor_state": {
                        "camp": 1,
                        "runtime_id": 4001,
                        "pos": {"x": 2000, "y": 0, "z": 3000},
                        "hp": 100,
                        "max_hp": 100
                    }
                },
                # 敌方小兵
                {
                    "sub_type": "ACTOR_SUB_soldier",
                    "actor_state": {
                        "camp": 2,
                        "runtime_id": 4002,
                        "pos": {"x": 9500, "y": 0, "z": 11500},
                        "hp": 80,
                        "max_hp": 100
                    }
                }
            ]
        },
        "legal_action": np.array([1, 1, 0, 1, 0, 1]),
        "reward": {"reward_sum": 0.0},
        "sub_action_mask": np.random.rand(6, 16)
    }
    return observation


def main():
    """主函数 - 演示如何使用增强版防御塔特征处理"""
    print("🚀 防御塔特征处理集成示例")
    print("="*80)
    
    # 1. 创建特征集成器
    integrator = FeatureIntegrationExample(camp=1)
    
    # 2. 创建测试数据
    observation = create_test_observation()
    
    # 3. 处理特征
    print("📊 处理防御塔特征...")
    tower_features = integrator.analyze_tower_features(observation)
    
    # 4. 展示特征统计信息
    print(f"\n📈 特征统计:")
    print(f"  总维度: {len(tower_features)}")
    print(f"  数值范围: [{min(tower_features):.3f}, {max(tower_features):.3f}]")
    print(f"  平均值: {np.mean(tower_features):.3f}")
    print(f"  标准差: {np.std(tower_features):.3f}")
    print(f"  非零特征数: {np.count_nonzero(tower_features)}")
    
    # 5. 展示各类特征的分布
    print(f"\n📋 特征分布:")
    feature_dims = integrator.enhanced_organ_processor.feature_dims
    start_idx = 0
    
    for feature_type, dim in feature_dims.items():
        end_idx = start_idx + dim
        feature_slice = tower_features[start_idx:end_idx]
        non_zero_count = np.count_nonzero(feature_slice)
        
        print(f"  {feature_type:20s}: {dim:2d}维, 非零: {non_zero_count:2d}, "
              f"范围: [{min(feature_slice):.3f}, {max(feature_slice):.3f}]")
        
        start_idx = end_idx
    
    # 6. 保存特征到文件（可选）
    print(f"\n💾 保存特征到文件...")
    np.save("tower_features_example.npy", tower_features)
    print(f"  已保存到: tower_features_example.npy")
    
    print(f"\n✅ 示例完成！")
    print("="*80)
    
    return tower_features


def integration_guide():
    """集成指南"""
    print("\n" + "="*80)
    print("🔧 集成指南")
    print("="*80)
    
    guide = """
    如何将增强版防御塔特征集成到现有系统：
    
    1. 📁 文件放置：
       - enhanced_organ_process.py -> agent_ppo/feature/feature_process/
       - enhanced_organ_feature_config.ini -> agent_ppo/feature/feature_process/
    
    2. 🔄 修改现有代码：
       在 agent_ppo/feature/feature_process/__init__.py 中：
       ```python
       from .enhanced_organ_process import EnhancedOrganProcess
       ```
    
    3. 🎯 在特征处理主函数中使用：
       ```python
       # 在 process_feature 函数中
       enhanced_organ_processor = EnhancedOrganProcess(camp)
       tower_features = enhanced_organ_processor.process_tower_features(frame_state)
       ```
    
    4. ⚙️ 配置调整：
       - 根据实际游戏数据调整归一化范围
       - 根据实际buff系统实现buff特征
       - 根据实际攻击系统实现攻击相关特征
    
    5. 🧪 测试验证：
       - 使用真实游戏数据测试特征提取
       - 验证特征维度和数值范围
       - 检查特征的有效性和稳定性
    
    6. 🔍 调试建议：
       - 使用 analyze_tower_features 函数分析特征
       - 检查各类特征的分布和统计信息
       - 对比新旧特征的差异
    """
    
    print(guide)
    print("="*80)


if __name__ == "__main__":
    # 运行主示例
    features = main()
    
    # 显示集成指南
    integration_guide()
