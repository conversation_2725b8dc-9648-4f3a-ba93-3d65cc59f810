{'env_id': '74595887', 
'player_id': 32, 
'player_camp': 2, 
'legal_action': [0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 
'sub_action_mask': [[1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 1.0, 1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 1.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 1.0, 1.0, 1.0], [1.0, 0.0, 0.0, 1.0, 1.0, 1.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 1.0, 1.0, 1.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 1.0]], 
'frame_state': {'frameNo': 56, 
    'hero_states': [{'player_id': 31, 

    'actor_state': {'config_id': 169, 'runtime_id': 7, 'actor_type': 'ACTOR_HERO', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'State_Idle', 'location': {'x': 100000, 'y': 100000, 'z': 100000}, 'forward': {'x': 100000, 'y': 100000, 'z': 100000}, 'hp': 3412, 'max_hp': 3412, 'values': {'phy_atk': 174, 'phy_def': 86, 'mgc_atk': 0, 'mgc_def': 50, 'mov_spd': 0, 'atk_spd': 0, 'ep': 0, 'max_ep': 440, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'attack_range': 8000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [True, False], 'sight_area': 7000, 'buff_state': {'buff_skills': [{'configId': 90015, 'times': 0, 'startTime': '1584'}]}}, 

    'skill_state': {'slot_states': [{'configId': 16900, 'slot_type': 'SLOT_SKILL_0', 'level': 0, 'usable': True, 'cooldown': 0, 'cooldown_max': 825, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16910, 'slot_type': 'SLOT_SKILL_1', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 10000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16920, 'slot_type': 'SLOT_SKILL_2', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 8000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16930, 'slot_type': 'SLOT_SKILL_3', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 45000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90003, 'slot_type': 'SLOT_SKILL_4', 'level': 0, 'usable': True, 'cooldown': 0, 'cooldown_max': 60000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 80115, 'slot_type': 'SLOT_SKILL_5', 'level': 0, 'usable': True, 'cooldown': 0, 'cooldown_max': 120000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90005, 'slot_type': 'SLOT_SKILL_6', 'level': 0, 'usable': True, 'cooldown': 0, 'cooldown_max': 0, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}]}, 
    

    'equip_state': {'equips': [{'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}]}, 
    
    'buff_state': {}, 
    
    'level': 1, 
    'exp': 0, 
    'money': 300, 
    'revive_time': 0, 'killCnt': 0, 'deadCnt': 0, 'assistCnt': 0, 'moneyCnt': 300, 'totalHurt': 0, 'totalHurtToHero': 0, 'totalBeHurtByHero': 0, 'passive_skill': [{'passive_skillid': 16901, 'cooldown': 0}, {'passive_skillid': 16902, 'cooldown': 0}], 'isInGrass': False, 'canBuyEquip': False}, {'player_id': 32, 'actor_state': {'config_id': 169, 'runtime_id': 13, 'actor_type': 'ACTOR_HERO', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Direction_Move', 'location': {'x': 39499, 'y': 48, 'z': 39951}, 'forward': {'x': -707, 'y': 0, 'z': -707}, 'hp': 3412, 'max_hp': 3412, 'values': {'phy_atk': 174, 'phy_def': 86, 'mgc_atk': 0, 'mgc_def': 50, 'mov_spd': 3780, 'atk_spd': 1620, 'ep': 440, 'max_ep': 440, 'hp_recover': 41, 'ep_recover': 16, 'phy_armor_hurt': 64, 'mgc_armor_hurt': 0, 'crit_rate': 560, 'crit_effe': 11080, 'phy_vamp': 800, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 8000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [False, True], 'sight_area': 7000, 'buff_state': {'buff_skills': [{'configId': 90015, 'times': 0, 'startTime': '1485'}]}}, 'skill_state': {'slot_states': [{'configId': 16900, 'slot_type': 'SLOT_SKILL_0', 'level': 1, 'usable': True, 'cooldown': 0, 'cooldown_max': 825, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16910, 'slot_type': 'SLOT_SKILL_1', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 10000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16920, 'slot_type': 'SLOT_SKILL_2', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 8000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16930, 'slot_type': 'SLOT_SKILL_3', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 45000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90003, 'slot_type': 'SLOT_SKILL_4', 'level': 1, 'usable': True, 'cooldown': 0, 'cooldown_max': 60000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 80115, 'slot_type': 'SLOT_SKILL_5', 'level': 1, 'usable': True, 'cooldown': 0, 'cooldown_max': 120000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90005, 'slot_type': 'SLOT_SKILL_6', 'level': 1, 'usable': True, 'cooldown': 0, 'cooldown_max': 0, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}]}, 'equip_state': {'equips': [{'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}]}, 'buff_state': {'buff_skills': [{'configId': 90015, 'times': 0, 'startTime': '1485'}]}, 'level': 1, 'exp': 0, 'money': 300, 'revive_time': 0, 'killCnt': 0, 'deadCnt': 0, 'assistCnt': 0, 'moneyCnt': 300, 'totalHurt': 0, 'totalHurtToHero': 0, 'totalBeHurtByHero': 0, 'passive_skill': [{'passive_skillid': 16901, 'cooldown': 0}, {'passive_skillid': 16902, 'cooldown': 0}], 'real_cmd': [{'command_type': 'COMMAND_TYPE_MoveDir', 'move_pos': {'destPos': {'x': 0, 'y': 0, 'z': 0}}, 'move_dir': {'degree': 135}, 'attack_common': {'actorID': 0, 'start': 0}, 'attack_topos': {'destPos': {'x': 0, 'y': 0, 'z': 0}}, 'attack_actor': {'actorID': 0}, 'obj_skill': {'skillID': 0, 'actorID': 0, 'slotType': 'SLOT_SKILL_0'}, 'dir_skill': {'skillID': 0, 'actorID': 0, 'slotType': 'SLOT_SKILL_0', 'degree': 0}, 'pos_skill': {'skillID': 0, 'destPos': {'x': 0, 'y': 0, 'z': 0}, 'slotType': 'SLOT_SKILL_0'}, 'learn_skill': {'slotType': 'SLOT_SKILL_0', 'level': 0}, 'buy_equip': {'equipId': 0, 'obj_id': 0}, 'sell_equip': {'equipIndex': 0}, 'charge_skill': {'slotType': 'SLOT_SKILL_0', 'state': 0, 'degree': 0}}], 'isInGrass': False, 'canBuyEquip': False}], 'npc_states': [{'config_id': 1114, 'runtime_id': 16, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_CRYSTAL', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Move', 'location': {'x': 31250, 'y': 0, 'z': 31350}, 'forward': {'x': 707, 'y': 0, 'z': -707}, 'hp': 9000, 'max_hp': 9000, 'values': {'phy_atk': 550, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 12000, 'attack_target': 0, 'kill_income': 0, 'camp_visible': [True, True], 'sight_area': 9300, 'buff_state': {}}, {'config_id': 1112, 'runtime_id': 15, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Move', 'location': {'x': 13131, 'y': 0, 'z': 13258}, 'forward': {'x': 707, 'y': 0, 'z': -707}, 'hp': 12000, 'max_hp': 12000, 'values': {'phy_atk': 470, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 8800, 'attack_target': 0, 'kill_income': 50, 'camp_visible': [True, True], 'sight_area': 8800, 'buff_state': {}}, {'config_id': 1111, 'runtime_id': 14, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Move', 'location': {'x': -13138, 'y': 0, 'z': -12940}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 12000, 'max_hp': 12000, 'values': {'phy_atk': 470, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 8800, 'attack_target': 0, 'kill_income': 50, 'camp_visible': [True, True], 'sight_area': 8800, 'buff_state': {}}, {'config_id': 1113, 'runtime_id': 9, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_CRYSTAL', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Move', 'location': {'x': -31310, 'y': 50, 'z': -31120}, 'forward': {'x': 0, 'y': 0, 'z': 1000}, 'hp': 9000, 'max_hp': 9000, 'values': {'phy_atk': 550, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 12000, 'attack_target': 0, 'kill_income': 0, 'camp_visible': [True, True], 'sight_area': 9300, 'buff_state': {}}, {'config_id': 46, 'runtime_id': 8, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER_SPRING', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Move', 'location': {'x': 45785, 'y': 1120, 'z': 45856}, 'forward': {'x': 0, 'y': 0, 'z': -1000}, 'hp': 6000, 'max_hp': 6000, 'values': {'phy_atk': 9999, 'phy_def': 0, 'mgc_atk': 0, 'mgc_def': 0, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 13000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [True, True], 'sight_area': 8000, 'buff_state': {}}, {'config_id': 44, 'runtime_id': 2, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER_SPRING', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Move', 'location': {'x': -45396, 'y': 1120, 'z': -45467}, 'forward': {'x': 0, 'y': 0, 'z': 1000}, 'hp': 6000, 'max_hp': 6000, 'values': {'phy_atk': 9999, 'phy_def': 0, 'mgc_atk': 0, 'mgc_def': 0, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 13000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [True, True], 'sight_area': 8000, 'buff_state': {}}], 'frame_action': {}, 'map_state': True}, 'win': 0, 'reward': {'tower_hp_point': 0.0, 'forward': -1.0113253865857972, 'reward_sum': -0.010113253865857971}}