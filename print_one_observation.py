#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
直接打印一帧完整observation的示例代码
"""

import numpy as np
import pprint
import json

def create_sample_observation():
    """创建一个示例observation，基于你之前看到的实际结构"""
    observation = {
        'env_id': 12345,
        'player_id': 0,
        'player_camp': 1,
        'win': 0,
        'legal_action': np.array([1, 1, 0, 1, 0, 1]),
        'sub_action_mask': np.random.rand(6, 16),
        'frame_state': {
            'frameNo': 100,
            'hero_states': [
                {
                    'actor_state': {
                        'camp': 1,
                        'hp': 800,
                        'max_hp': 1000,
                        'level': 5,
                        'pos': {'x': 10.5, 'y': 20.3, 'z': 0.0},
                        'runtime_id': 1001
                    },
                    'skill_states': [
                        {'skill_id': 1, 'cd_time': 0, 'level': 3},
                        {'skill_id': 2, 'cd_time': 5, 'level': 2}
                    ]
                },
                {
                    'actor_state': {
                        'camp': 2,
                        'hp': 950,
                        'max_hp': 1000,
                        'level': 4,
                        'pos': {'x': 50.2, 'y': 60.8, 'z': 0.0},
                        'runtime_id': 2001
                    },
                    'skill_states': [
                        {'skill_id': 1, 'cd_time': 2, 'level': 2},
                        {'skill_id': 2, 'cd_time': 0, 'level': 3}
                    ]
                }
            ],
            'npc_states': [
                {
                    'actor_state': {
                        'camp': 1,
                        'hp': 100,
                        'max_hp': 100,
                        'pos': {'x': 15.0, 'y': 25.0, 'z': 0.0}
                    },
                    'sub_type': 'ACTOR_SUB_soldier',
                    'runtime_id': 3001
                }
            ]
        },
        'reward': {
            'reward_sum': 10.5,
            'kill_reward': 5.0,
            'damage_reward': 3.0,
            'position_reward': 2.5
        }
    }
    return observation

def print_observation_method1(observation):
    """方法1：使用pprint直接打印"""
    print("\n" + "="*100)
    print("METHOD 1: Using pprint.pprint()")
    print("="*100)
    
    pprint.pprint(observation, width=120, depth=10)
    
    print("="*100)

def print_observation_method2(observation):
    """方法2：使用print直接打印"""
    print("\n" + "="*100)
    print("METHOD 2: Using print() directly")
    print("="*100)
    
    print(observation)
    
    print("="*100)

def print_observation_method3(observation):
    """方法3：转换为JSON格式打印"""
    print("\n" + "="*100)
    print("METHOD 3: Convert to JSON format")
    print("="*100)
    
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    try:
        converted_obs = convert_numpy(observation)
        print(json.dumps(converted_obs, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"Error converting to JSON: {e}")
        print(observation)
    
    print("="*100)

def print_observation_method4(observation):
    """方法4：逐行打印每个键值对"""
    print("\n" + "="*100)
    print("METHOD 4: Print each key-value pair")
    print("="*100)
    
    def print_dict_recursive(d, indent=0):
        for key, value in d.items():
            spaces = "  " * indent
            if isinstance(value, dict):
                print(f"{spaces}{key}:")
                print_dict_recursive(value, indent + 1)
            elif isinstance(value, list):
                print(f"{spaces}{key}: [list with {len(value)} items]")
                if len(value) > 0 and isinstance(value[0], dict):
                    for i, item in enumerate(value[:2]):  # 只显示前2个
                        print(f"{spaces}  [{i}]:")
                        print_dict_recursive(item, indent + 2)
                    if len(value) > 2:
                        print(f"{spaces}  ... and {len(value) - 2} more items")
                else:
                    print(f"{spaces}  {value}")
            elif hasattr(value, 'shape'):  # numpy array
                print(f"{spaces}{key}: {type(value).__name__} shape={value.shape}")
                if value.size <= 20:
                    print(f"{spaces}  values: {value.tolist()}")
                else:
                    print(f"{spaces}  first 10 values: {value.flatten()[:10].tolist()}")
            else:
                print(f"{spaces}{key}: {value}")
    
    print_dict_recursive(observation)
    print("="*100)

def main():
    """主函数"""
    print("DEMONSTRATION: How to print one complete observation")
    
    # 创建示例observation
    observation = create_sample_observation()
    
    # 方法1：使用pprint
    print_observation_method1(observation)
    
    # 方法2：直接print
    print_observation_method2(observation)
    
    # 方法3：JSON格式
    print_observation_method3(observation)
    
    # 方法4：递归打印
    print_observation_method4(observation)
    
    print("\n" + "="*100)
    print("SUMMARY: Choose the method that works best for your needs!")
    print("- Method 1 (pprint): Best for readable formatting")
    print("- Method 2 (print): Simplest, one line of code")
    print("- Method 3 (JSON): Good for saving to files")
    print("- Method 4 (recursive): Most detailed breakdown")
    print("="*100)

if __name__ == "__main__":
    main()
