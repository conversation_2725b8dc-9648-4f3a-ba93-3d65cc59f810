#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
简单的observation结构测试
"""

import numpy as np
import json

def create_sample_observation():
    """创建一个示例observation"""
    observation = {
        "frame_state": {
            "frameNo": 100,
            "hero_states": [
                {
                    "actor_state": {
                        "camp": 1,
                        "hp": 800,
                        "max_hp": 1000,
                        "level": 5,
                        "pos": {"x": 10.5, "y": 20.3, "z": 0.0},
                        "runtime_id": 1001
                    },
                    "skill_states": [
                        {"skill_id": 1, "cd_time": 0, "level": 3},
                        {"skill_id": 2, "cd_time": 5, "level": 2}
                    ]
                },
                {
                    "actor_state": {
                        "camp": 2,
                        "hp": 950,
                        "max_hp": 1000,
                        "level": 4,
                        "pos": {"x": 50.2, "y": 60.8, "z": 0.0},
                        "runtime_id": 2001
                    },
                    "skill_states": [
                        {"skill_id": 1, "cd_time": 2, "level": 2},
                        {"skill_id": 2, "cd_time": 0, "level": 3}
                    ]
                }
            ],
            "npc_states": [
                {
                    "actor_state": {
                        "camp": 1,
                        "hp": 100,
                        "max_hp": 100,
                        "pos": {"x": 15.0, "y": 25.0, "z": 0.0}
                    },
                    "sub_type": "ACTOR_SUB_soldier",
                    "runtime_id": 3001
                },
                {
                    "actor_state": {
                        "camp": 2,
                        "hp": 80,
                        "max_hp": 100,
                        "pos": {"x": 45.0, "y": 55.0, "z": 0.0}
                    },
                    "sub_type": "ACTOR_SUB_soldier",
                    "runtime_id": 3002
                }
            ]
        },
        "legal_action": np.array([1, 1, 0, 1, 0, 1]),
        "reward": {
            "reward_sum": 10.5,
            "kill_reward": 5.0,
            "damage_reward": 3.0,
            "position_reward": 2.5
        },
        "sub_action_mask": np.random.rand(6, 16),
        "observation": np.random.rand(95)
    }
    return observation

def print_observation_structure(observation):
    """打印observation的详细结构"""
    print("\n" + "="*80)
    print("🎮 OBSERVATION STRUCTURE ANALYSIS")
    print("="*80)
    
    # 1. 顶层结构
    print(f"\n📋 Top Level Keys: {list(observation.keys())}")
    print(f"📊 Total Keys: {len(observation.keys())}")
    
    # 2. frame_state详细分析
    if "frame_state" in observation:
        frame_state = observation["frame_state"]
        print(f"\n🎯 Frame State Analysis:")
        print(f"   Frame No: {frame_state.get('frameNo', 'N/A')}")
        print(f"   Keys: {list(frame_state.keys())}")
        
        # 英雄状态
        if "hero_states" in frame_state:
            heroes = frame_state["hero_states"]
            print(f"\n👥 Heroes Analysis ({len(heroes)} heroes):")
            for i, hero in enumerate(heroes):
                print(f"   Hero {i}:")
                print(f"     Keys: {list(hero.keys())}")
                
                if "actor_state" in hero:
                    actor = hero["actor_state"]
                    print(f"     Actor State:")
                    print(f"       Camp: {actor.get('camp', 'N/A')}")
                    print(f"       HP: {actor.get('hp', 0)}/{actor.get('max_hp', 1)}")
                    print(f"       Level: {actor.get('level', 'N/A')}")
                    print(f"       Position: {actor.get('pos', {})}")
                    print(f"       Runtime ID: {actor.get('runtime_id', 'N/A')}")
                
                if "skill_states" in hero:
                    skills = hero["skill_states"]
                    print(f"     Skills ({len(skills)} skills):")
                    for j, skill in enumerate(skills):
                        print(f"       Skill {j}: ID={skill.get('skill_id', 'N/A')}, "
                              f"CD={skill.get('cd_time', 0)}, Level={skill.get('level', 'N/A')}")
        
        # NPC状态
        if "npc_states" in frame_state:
            npcs = frame_state["npc_states"]
            print(f"\n🤖 NPCs Analysis ({len(npcs)} NPCs):")
            for i, npc in enumerate(npcs):
                print(f"   NPC {i}:")
                print(f"     Keys: {list(npc.keys())}")
                print(f"     Sub Type: {npc.get('sub_type', 'N/A')}")
                print(f"     Runtime ID: {npc.get('runtime_id', 'N/A')}")
                
                if "actor_state" in npc:
                    actor = npc["actor_state"]
                    print(f"     Actor State:")
                    print(f"       Camp: {actor.get('camp', 'N/A')}")
                    print(f"       HP: {actor.get('hp', 0)}/{actor.get('max_hp', 1)}")
                    print(f"       Position: {actor.get('pos', {})}")
    
    # 3. legal_action分析
    if "legal_action" in observation:
        legal_action = observation["legal_action"]
        print(f"\n⚖️ Legal Action Analysis:")
        print(f"   Type: {type(legal_action)}")
        if hasattr(legal_action, 'shape'):
            print(f"   Shape: {legal_action.shape}")
            print(f"   Data Type: {legal_action.dtype}")
            print(f"   Valid Actions: {np.sum(legal_action)}")
            print(f"   Actions: {legal_action.tolist()}")
    
    # 4. reward分析
    if "reward" in observation:
        reward = observation["reward"]
        print(f"\n🏆 Reward Analysis:")
        print(f"   Type: {type(reward)}")
        if isinstance(reward, dict):
            print(f"   Keys: {list(reward.keys())}")
            for key, value in reward.items():
                print(f"   {key}: {value}")
        else:
            print(f"   Value: {reward}")
    
    # 5. sub_action_mask分析
    if "sub_action_mask" in observation:
        sub_action_mask = observation["sub_action_mask"]
        print(f"\n🎭 Sub Action Mask Analysis:")
        print(f"   Type: {type(sub_action_mask)}")
        if hasattr(sub_action_mask, 'shape'):
            print(f"   Shape: {sub_action_mask.shape}")
            print(f"   Data Type: {sub_action_mask.dtype}")
            print(f"   Min Value: {np.min(sub_action_mask):.4f}")
            print(f"   Max Value: {np.max(sub_action_mask):.4f}")
            print(f"   Mean Value: {np.mean(sub_action_mask):.4f}")
    
    # 6. observation向量分析
    if "observation" in observation:
        obs_vec = observation["observation"]
        print(f"\n📊 Observation Vector Analysis:")
        print(f"   Type: {type(obs_vec)}")
        if hasattr(obs_vec, 'shape'):
            print(f"   Shape: {obs_vec.shape}")
            print(f"   Data Type: {obs_vec.dtype}")
            print(f"   Min Value: {np.min(obs_vec):.4f}")
            print(f"   Max Value: {np.max(obs_vec):.4f}")
            print(f"   Mean Value: {np.mean(obs_vec):.4f}")
            print(f"   First 10 values: {obs_vec[:10].tolist()}")
    
    print("="*80)

def save_observation_to_file(observation, filename="sample_observation.json"):
    """保存observation到JSON文件"""
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    try:
        converted_obs = convert_numpy(observation)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(converted_obs, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Observation saved to {filename}")
    except Exception as e:
        print(f"\n❌ Error saving observation: {e}")

def main():
    """主函数"""
    print("🚀 Starting Simple Observation Test...")
    
    # 创建示例observation
    observation = create_sample_observation()
    
    # 打印详细结构
    print_observation_structure(observation)
    
    # 保存到文件
    save_observation_to_file(observation)
    
    print("\n✅ Test completed successfully!")
    print("\n💡 Tips for debugging in real training:")
    print("   1. Add print statements in agent.py predict() function")
    print("   2. Add print statements in train_workflow.py")
    print("   3. Use environment variable to control debug output")
    print("   4. Save observations to files for detailed analysis")

if __name__ == "__main__":
    main()
