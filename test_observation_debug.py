#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试observation调试功能
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.append('/data/projects/hok1v1')

def create_mock_observation():
    """创建模拟的observation数据"""
    observation = {
        "frame_state": {
            "frameNo": 1,
            "hero_states": [
                {
                    "actor_state": {
                        "camp": 1,
                        "hp": 1000,
                        "max_hp": 1000,
                        "level": 1,
                        "pos": {"x": 10.0, "y": 20.0, "z": 0.0}
                    },
                    "skill_states": [
                        {"skill_id": 1, "cd_time": 0},
                        {"skill_id": 2, "cd_time": 5}
                    ]
                },
                {
                    "actor_state": {
                        "camp": 2,
                        "hp": 950,
                        "max_hp": 1000,
                        "level": 1,
                        "pos": {"x": 50.0, "y": 60.0, "z": 0.0}
                    },
                    "skill_states": [
                        {"skill_id": 1, "cd_time": 2},
                        {"skill_id": 2, "cd_time": 0}
                    ]
                }
            ],
            "npc_states": [
                {
                    "actor_state": {
                        "camp": 1,
                        "hp": 100,
                        "max_hp": 100
                    },
                    "sub_type": "ACTOR_SUB_soldier"
                }
            ]
        },
        "legal_action": np.array([1, 1, 0, 1, 0, 1]),
        "reward": {
            "reward_sum": 0.5,
            "kill_reward": 0.0,
            "damage_reward": 0.3,
            "position_reward": 0.2
        },
        "sub_action_mask": np.random.rand(6, 16),
        "observation": np.random.rand(95)  # 特征向量
    }
    return observation

def simple_debug_observation(observation):
    """简单的observation调试函数"""
    print("\n" + "="*80)
    print("🎮 OBSERVATION DEBUG TEST")
    print("="*80)
    
    # 基本信息
    print(f"📋 Observation Keys: {list(observation.keys())}")
    
    # Frame State
    if "frame_state" in observation:
        frame_state = observation["frame_state"]
        print(f"\n🎯 Frame State:")
        print(f"   Frame No: {frame_state.get('frameNo', 'N/A')}")
        print(f"   Keys: {list(frame_state.keys())}")
        
        # 英雄信息
        if "hero_states" in frame_state:
            heroes = frame_state["hero_states"]
            print(f"\n👥 Heroes ({len(heroes)} total):")
            for i, hero in enumerate(heroes):
                actor_state = hero.get("actor_state", {})
                camp = actor_state.get("camp", "N/A")
                hp = actor_state.get("hp", 0)
                max_hp = actor_state.get("max_hp", 1)
                level = actor_state.get("level", "N/A")
                pos = actor_state.get("pos", {})
                
                print(f"   Hero {i}:")
                print(f"     Camp: {camp}")
                print(f"     HP: {hp}/{max_hp} ({hp/max_hp*100:.1f}%)")
                print(f"     Level: {level}")
                print(f"     Position: ({pos.get('x', 0):.1f}, {pos.get('y', 0):.1f}, {pos.get('z', 0):.1f})")
                
                # 技能信息
                if "skill_states" in hero:
                    skills = hero["skill_states"]
                    print(f"     Skills: {len(skills)} skills")
                    for j, skill in enumerate(skills):
                        skill_id = skill.get("skill_id", "N/A")
                        cd_time = skill.get("cd_time", 0)
                        print(f"       Skill {j}: ID={skill_id}, CD={cd_time}")
        
        # NPC信息
        if "npc_states" in frame_state:
            npcs = frame_state["npc_states"]
            print(f"\n🤖 NPCs ({len(npcs)} total):")
            for i, npc in enumerate(npcs[:3]):  # 只显示前3个
                actor_state = npc.get("actor_state", {})
                camp = actor_state.get("camp", "N/A")
                hp = actor_state.get("hp", 0)
                sub_type = npc.get("sub_type", "N/A")
                print(f"   NPC {i}: Camp={camp}, HP={hp}, Type={sub_type}")
    
    # Legal Action
    if "legal_action" in observation:
        legal_action = observation["legal_action"]
        print(f"\n⚖️ Legal Action:")
        print(f"   Shape: {legal_action.shape}")
        print(f"   Valid Actions: {np.sum(legal_action)}")
        print(f"   Actions: {legal_action.tolist()}")
    
    # Reward
    if "reward" in observation:
        reward = observation["reward"]
        print(f"\n🏆 Reward:")
        if isinstance(reward, dict):
            for key, value in reward.items():
                print(f"   {key}: {value}")
        else:
            print(f"   Reward: {reward}")
    
    # Sub Action Mask
    if "sub_action_mask" in observation:
        sub_action_mask = observation["sub_action_mask"]
        print(f"\n🎭 Sub Action Mask:")
        print(f"   Shape: {sub_action_mask.shape}")
        print(f"   Data Type: {sub_action_mask.dtype}")
    
    # Observation Vector
    if "observation" in observation:
        obs_vec = observation["observation"]
        print(f"\n📊 Observation Vector:")
        print(f"   Shape: {obs_vec.shape}")
        print(f"   Data Type: {obs_vec.dtype}")
        print(f"   First 10 values: {obs_vec[:10].tolist()}")
    
    print("="*80)

def test_agent_debug():
    """测试Agent中的调试功能"""
    try:
        from agent_ppo.agent import Agent
        
        print("🧪 Testing Agent Debug Function...")
        
        # 创建Agent实例
        agent = Agent(agent_type="player", device="cpu")
        
        # 创建模拟observation
        observation = create_mock_observation()
        
        # 测试调试函数
        if hasattr(agent, '_debug_print_observation'):
            print("✅ Found _debug_print_observation method")
            agent._debug_print_observation(observation)
        else:
            print("❌ _debug_print_observation method not found")
            
    except Exception as e:
        print(f"❌ Error testing agent debug: {e}")

def main():
    """主函数"""
    print("🚀 Starting Observation Debug Test...")
    
    # 1. 测试基本的observation调试
    print("\n1. Testing Basic Observation Debug:")
    observation = create_mock_observation()
    simple_debug_observation(observation)
    
    # 2. 测试Agent中的调试功能
    print("\n2. Testing Agent Debug Function:")
    test_agent_debug()
    
    print("\n✅ Debug test completed!")

if __name__ == "__main__":
    main()
