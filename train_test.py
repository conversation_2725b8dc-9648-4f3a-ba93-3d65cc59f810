#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import time
import os
import platform
import glob
from multiprocessing import Process # 用于创建和管理子进程，实现多进程并发执行任务。
from kaiwudrl.common.utils.kaiwudrl_define import KaiwuDRLDefine # 定义了一些常量、枚举或全局配置，供整个框架使用。
from kaiwudrl.common.utils.http_utils import http_utils_request #用于发起 HTTP 请求，通常用于服务间通信或远程调用。
from kaiwudrl.common.utils.common_func import (
    python_exec_shell,
    find_pids_by_cmdline,
    scan_for_errors,
) # 一些通用的函数，如执行shell命令、查找进程ID、扫描错误日志等。
import kaiwudrl.server.learner.learner as learner 
import kaiwudrl.server.aisrv.aisrv as aisrv # 分别是学习器（Learner）和 AI 服务（AISrv）的主模块，通常包含主流程入口
from kaiwudrl.common.config.config_control import CONFIG # 用于管理配置文件的加载和解析
from typing import List # 用于类型注释

# To run the train_test, you must modify the algorithm name here. It must be one of ppo, diy.
# Simply modify the value of the algorithm_name variable.
# 运行train_test前必须修改这里的算法名字, 必须是ppo、diy里的一个, 修改algorithm_name的值即可
algorithm_name_list = ["ppo", "diy"]
algorithm_name = "ppo"


# train
# 训练
def train():

    start_time = time.time()

    # To modify the value in the environment variable and initiate training for the learner as soon as possible
    # 修改环境变量里的值, 尽快让learner进行训练
    os.environ.update(
        {
            "use_ckpt_sync": "False",
            "replay_buffer_capacity": "10", #样本池容量
            "preload_ratio": "1.0", # 预加载比例，设为1.0表示全部预加载，让训练可以立即使用全部数据，进一步加快训练启动。
            "train_batch_size": "2", #训练批次大小，设为2，极小的batch，可以让训练循环更快进行，适合测试流程。
            "use_prometheus": "True", # 是否使用prometheus上报监控指标, 测试流程建议关闭, 减少对系统的影响
            "aisrv_connect_to_kaiwu_env_count": "1", # aisrv连接到kaiwu环境的次数, 测试流程设为1即可 负责使用训练好的模型进行推理和决策，AISrv 是与 Learner 并行启动的独立进程，是分布式强化学习系统的重要组成部分。
            "dump_model_freq": "1", # 模型保存频率，设为1，每训练一次就保存一次模型，方便快速检测模型保存流程是否正常。
            "is_train_test": "True",
        }
    )

    # To modify the configuration and execute the script directly
    # 修改配置, 调用脚本直接执行
    if algorithm_name not in algorithm_name_list:
        print("\033[92m" + f"algorithm_name: {algorithm_name} not in list {algorithm_name_list}" + "\033[0m")

        python_exec_shell(f"sh tools/stop.sh all") # 调用外部 shell 脚本 tools/stop.sh，并传入参数 all，停止所有正在运行的服务或进程

    python_exec_shell(f"sh /root/tools/change_algorithm_all.sh {algorithm_name}") # 调用外部 shell 脚本 tools/change_algorithm_all.sh，并传入参数 algorithm_name，修改所有相关配置为指定的算法
    print(f"current algorithm_name is {algorithm_name}")

    # Setting the sample transmission to either Reverb or ZMQ based on the operating system
    # 根据不同的操作系统设置发送样本的是reverb还是zmq
    architecture = platform.machine()
    platform_maps = {
        "aarch64": "zmq",
        "arm64": "zmq",
        "x86_64": "reverb",
        "AMD64": "reverb",
    }
    sample_tool_type = platform_maps.get(architecture)
    if sample_tool_type is None:
        print(f"Architecture '{architecture}' may not exist or not be supported.")
    else:
        result_code, result_str = python_exec_shell(f"sh tools/change_sample_server.sh {sample_tool_type}")
        if result_code != 0:
            raise ValueError(f"Execution error! Please check the error detail: {result_str}")

    CONFIG.set_configure_file("conf/kaiwudrl/learner.toml")
    CONFIG.parse_learner_configure()

    # To delete previous model files and logs
    # 删除以前的model文件和日志
    python_exec_shell(f"rm -rf {CONFIG.user_ckpt_dir}/{CONFIG.app}_{algorithm_name}/*")
    python_exec_shell(f"rm -rf {CONFIG.log_dir}/*")
    done_file = f"/data/ckpt/{CONFIG.app}_{CONFIG.algo}/process_stop.done"
    if os.path.exists(done_file):
        os.remove(done_file)

    # To delete previous model files
    # 删除以前的model文件
    model_files = glob.glob(f"/data/ckpt/{CONFIG.app}_{CONFIG.algo}/model.ckpt-*.pkl")
    if model_files:
        for file in model_files:
            os.remove(file)

    # To start aisrv, learner, actor, battlesrv, modelpool
    # 启动aisrv, learner, actor, battlesrv, modelpool
    procs: List[Process] = []
    procs.append(Process(target=learner.main, name="learner"))
    procs.append(Process(target=aisrv.main, name="aisrv"))

    python_exec_shell(f"sh tools/modelpool_start.sh learner")

    for proc in procs:
        proc.start()
        time.sleep(10)
        check(proc)

    while True:

        error_msg = scan_for_errors(CONFIG.log_dir, error_indicator="ERROR")
        train_success = check_train_success_by_model_file()
        process_done = check_process_stop_done()

        # If an error log is generated, exit early
        # 如果有错误日志产生, 提前退出
        if error_msg:
            time.sleep(5)
            print("\033[1;31m" + "find error log, please check" + "\033[0m")
            python_exec_shell(f"sh tools/stop.sh all")

        else:
            if train_success or process_done:

                time.sleep(5)
                print(
                    "\033[1;32m"
                    + "Train test succeeded, will exit, cost "
                    + f"{time.time() - start_time:.2f} seconds"
                    + "\033[0m"
                )
                python_exec_shell("sh tools/stop.sh all")

        time.sleep(1)
        for proc in procs:
            check(proc)


def check_process_stop_done():
    done_file = f"/data/ckpt/{CONFIG.app}_{CONFIG.algo}/process_stop.done"
    if os.path.exists(done_file):
        return True

    return False


# To check if a process is alive, any error log
# 检测进程是否存活, 是否有错误日志
def check(proc: Process):
    if not proc.is_alive():
        time.sleep(5)
        print("\033[1;31m" + f"{proc.name} is not alive, please check error log" + "\033[0m")
        python_exec_shell(f"sh tools/stop.sh all")
    else:
        print(f"{proc.name} is alive")


# Judge based on the landed model file, if learner successfully save the model file, it is considered as training success
# 按照落地的model文件来做判断, 如果learner正确保存model文件视为训练成功
def check_train_success_by_model_file():
    model_files = glob.glob(f"/data/ckpt/{CONFIG.app}_{CONFIG.algo}/model.ckpt-[1-9]*.pkl")
    if model_files:
        for file in model_files:
            if os.path.exists(file):
                os.remove(file)
        return True

    return False


if __name__ == "__main__":
    try:
        train()
    except KeyboardInterrupt:
        print("\033[1;31m" + "KeyboardInterrupt, please check" + "\033[0m")
        python_exec_shell(f"sh tools/stop.sh all")
